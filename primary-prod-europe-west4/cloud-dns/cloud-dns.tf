resource "google_dns_managed_zone" "bodyscratch_academy" {
  project     = local.project_id
  name        = "bodyscratch-academy"
  dns_name    = "bodyscratch.academy."
  description = "Managed zone for bodyscratch.academy"
  visibility  = "public"

  dnssec_config {
    state = "on"
  }
}

# Root A record
resource "google_dns_record_set" "root_a" {
  project      = local.project_id
  name         = "bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 86400
  rrdatas      = ["*************"]
}

# Root AAAA record
resource "google_dns_record_set" "root_aaaa" {
  project      = local.project_id
  name         = "bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "AAAA"
  ttl          = 86400
  rrdatas      = ["2600:1901:0:265e::"]
}

# WWW A record
resource "google_dns_record_set" "www_a" {
  project      = local.project_id
  name         = "www.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 14400
  rrdatas      = ["*************"]
}

# WWW AAAA record
resource "google_dns_record_set" "www_aaaa" {
  project      = local.project_id
  name         = "www.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "AAAA"
  ttl          = 14400
  rrdatas      = ["2600:1901:0:265e::"]
}

# MX records
resource "google_dns_record_set" "root_mx" {
  project      = local.project_id
  name         = "bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "MX"
  ttl          = 300
  rrdatas      = ["1 smtp.google.com."]
}

# SPF record
resource "google_dns_record_set" "root_spf" {
  project      = local.project_id
  name         = "bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "SPF"
  ttl          = 300
  rrdatas      = ["\"v=spf1 include:mx.ovh.com ~all\""]
}

# TXT records
resource "google_dns_record_set" "root_txt" {
  project      = local.project_id
  name         = "bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "TXT"
  ttl          = 14400
  rrdatas      = ["\"v=spf1 ip4:************ a mx include:websitewelcome.com ~all\""]
}

# Google verification CNAME
resource "google_dns_record_set" "google_verification" {
  project      = local.project_id
  name         = "7hh5upnkixpa.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "CNAME"
  ttl          = 300
  rrdatas      = ["gv-4x37jbze3yp5wp.dv.googlehosted.com."]
}

# ACME challenge TXT
resource "google_dns_record_set" "acme_challenge" {
  project      = local.project_id
  name         = "_acme-challenge.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "TXT"
  ttl          = 60
  rrdatas      = ["\"lr02iXVJtEpMsBW-2Tt9ptbuFISit1vsQXka5qQIPeE\""]
}

# DKIM TXT record
resource "google_dns_record_set" "dkim" {
  project      = local.project_id
  name         = "default._domainkey.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "TXT"
  ttl          = 14400
  rrdatas      = ["\"v=DKIM1; k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAy+DfT9UefRAUAnXEyQwc/VDekwD1ekhK3pgoc8AIURK/fK67D4X2jEcMqYTYwZFTQOez+UluCI5C2lV1wHEMMb/MNCV42qZAtAQk28x++yGWeOSO1bN/wzUzAPmZRtR9jzQzMqqVrVEGURrtbxen77ht4G6ftrTZURQacSH1dshIFFgsiV5FEGVhsyejk8x1y\" \"Hy6/axU9ZP2sz6MUlUunWlGZ8g3EsFroX1bmVYPP8aQU3yKlfZjZI4ugIc4XN8kv9s+MQhfKESP1ZXFNvp0dQD/BrvFqwMA2gbQzhA1cphPPHDeI69UF9qeuEPHfxGWfQVrzaxz3BbxFusxHCSpUQIDAQAB;\""]
}

# SRV records
resource "google_dns_record_set" "autodiscover_srv" {
  project      = local.project_id
  name         = "_autodiscover._tcp.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "SRV"
  ttl          = 14400
  rrdatas      = ["0 0 443 cpanelemaildiscovery.cpanel.net."]
}

# API records
resource "google_dns_record_set" "api" {
  project      = local.project_id
  name         = "api.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 300
  rrdatas      = ["34.13.79.163"]
}

# App records
resource "google_dns_record_set" "app" {
  project      = local.project_id
  name         = "app.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 300
  rrdatas      = ["34.8.220.134"]
}

# Record-App records
resource "google_dns_record_set" "record-app" {
  project      = local.project_id
  name         = "record-app.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 300
  rrdatas      = ["34.36.207.210"]
}

# Record-Control-App records
resource "google_dns_record_set" "record-control-app" {
  project      = local.project_id
  name         = "record-control-app.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 300
  rrdatas      = ["34.120.230.139"]
}

# Keycloak records
resource "google_dns_record_set" "keycloak" {
  project      = local.project_id
  name         = "keycloak.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 300
  rrdatas      = ["34.107.219.18"]
}

# Live records
resource "google_dns_record_set" "live" {
  project      = local.project_id
  name         = "live.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 300
  rrdatas      = ["34.54.238.217"]
}

# Turn Live records
resource "google_dns_record_set" "turn_live" {
  project      = local.project_id
  name         = "turn.live.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 300
  rrdatas      = ["34.91.62.149"]
}

# Media records
resource "google_dns_record_set" "media" {
  project      = local.project_id
  name         = "media.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 300
  rrdatas      = ["34.95.124.195"]
}

# Mailgun records
resource "google_dns_record_set" "mg_mx" {
  project      = local.project_id
  name         = "mg.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "MX"
  ttl          = 300
  rrdatas = [
    "10 mxa.mailgun.org.",
    "10 mxb.mailgun.org."
  ]
}

resource "google_dns_record_set" "mg_txt" {
  project      = local.project_id
  name         = "mg.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "TXT"
  ttl          = 300
  rrdatas      = ["\"v=spf1 include:mailgun.org ~all\""]
}

resource "google_dns_record_set" "mg_dkim" {
  project      = local.project_id
  name         = "krs._domainkey.mg.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "TXT"
  ttl          = 300
  rrdatas      = ["\"k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDyEYIE6iT1jyuab7vHKRbSQQzEZXgtqGinkpoS3cPFFwR4cwBaSR1JZflJEKOfeBpy+Vxg250NCwHD4g93FBDGABWQewivriCaIjLCG9AOIH/RnJmbypgOVgDXl/ZbyQ9bVMMZ7dj+cM7KPOwR4q87mtq41Q818WA/XlmiYheLbwIDAQAB\""]
}

resource "google_dns_record_set" "mg_email" {
  project      = local.project_id
  name         = "email.mg.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "CNAME"
  ttl          = 300
  rrdatas      = ["mailgun.org."]
}

# Play records
resource "google_dns_record_set" "play" {
  project      = local.project_id
  name         = "play.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 300
  rrdatas      = ["34.8.38.223"]
}

resource "google_dns_record_set" "www_play" {
  project      = local.project_id
  name         = "www.play.bodyscratch.academy."
  managed_zone = google_dns_managed_zone.bodyscratch_academy.name
  type         = "A"
  ttl          = 300
  rrdatas      = ["34.13.155.181"]
}