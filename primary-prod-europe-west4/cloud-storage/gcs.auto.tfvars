project_name    = "BodyScratch Pro --europe-west4"
base_project_id = "bodyscratch-prod-eu"
region          = "europe-west4"
lifecycle_rules = [
  {
    action = {
      type = "Delete"
    }
    condition = {
      age                        = 0
      days_since_custom_time     = 0
      days_since_noncurrent_time = 0
      num_newer_versions         = 15
      with_state                 = "ARCHIVED"
      # matches_prefix, matches_storage_class, and matches_suffix are omitted entirely if not needed
    }
  },
  {
    action = {
      type = "Delete"
    }
    condition = {
      age                        = 0
      days_since_custom_time     = 0
      days_since_noncurrent_time = 30
      num_newer_versions         = 0
      with_state                 = "ANY"
      # matches_prefix, matches_storage_class, and matches_suffix are omitted entirely if not needed
    }
  }
]