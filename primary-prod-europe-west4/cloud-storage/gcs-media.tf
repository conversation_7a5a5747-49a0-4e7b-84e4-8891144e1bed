## media.bodyscratch.academy
module "gcs_media" {
  source        = "git::**************:BodyScratch/gcp-terraform-modules.git//cloud-storage?ref=main"
  project_id    = local.project_id
  name          = "bodyscratch-media-website"
  location      = "EU"
  storage_class = "STANDARD"
  iam_members   = [
    {
      role      = "roles/storage.legacyObjectReader"
      member    = "allUsers"  # Public access
    },
    {
      role      = "roles/storage.objectAdmin"
      member    = "serviceAccount:api-sa@${local.project_id}.iam.gserviceaccount.com"
    }
  ]
  labels        = {
    "purpose"   = "media"
  }
  versioning    = false
  autoclass     = false
  cors          = [
    {
      max_age_seconds  = 3600
      method          = ["GET"]
      origin          = ["*"]  # TODO: Change this to the actual domain
      response_header = []
    }
  ]
}

# Reserve an external IP for the load balancer
resource "google_compute_global_address" "media-external-ip" {
  name         = "media-global-external-ip"
  description  = "media.bodyscratch.academy External IP"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV4"
  labels = {
    "bucket" = "media"
  }
}

# Add the bucket as a CDN backend
resource "google_compute_backend_bucket" "media-backend-bucket" {
  name        = "media-backend-bucket"
  description = "media-backend-bucket"
  project     = local.project_id
  bucket_name = module.gcs_media.name
  enable_cdn  = true
}

# Create HTTPS certificate
resource "google_compute_managed_ssl_certificate" "media-ssl-cert" {
  name          = "media-ssl-cert"
  description   = "SSL Cert for Keycloak"
  project       = local.project_id
  managed {
    domains = ["media.bodyscratch.academy"]
  }
}

# URL MAP
resource "google_compute_url_map" "media-url-map" {
  name            = "media-url-map"
  default_service = google_compute_backend_bucket.media-backend-bucket.self_link
  project         = local.project_id
}

# Target HTTPS Proxy
resource "google_compute_target_https_proxy" "media-target-proxy" {
  name             = "media-target-proxy"
  project          = local.project_id
  url_map          = google_compute_url_map.media-url-map.self_link
  ssl_certificates = [google_compute_managed_ssl_certificate.media-ssl-cert.self_link]
}

# Forwarding Rule
resource "google_compute_global_forwarding_rule" "media-forwarding-rule" {
  name                  = "media-forwarding-rule"
  project               = local.project_id
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.media-external-ip.address
  ip_protocol           = "TCP"
  port_range            = "443"
  target                = google_compute_target_https_proxy.media-target-proxy.self_link
}