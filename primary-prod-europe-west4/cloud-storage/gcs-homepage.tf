## bodyscratch.academy
module "gcs_bsc-homepage" {
  source        = "git::**************:BodyScratch/gcp-terraform-modules.git//cloud-storage?ref=main"
  project_id    = local.project_id
  name          = "bsc-homepage"
  location      = "EU"
  storage_class = "STANDARD"
  iam_members   = [
    {
      role      = "roles/storage.legacyObjectReader"
      member    = "allUsers"  # Public access
    },
  ]
  labels        = {
    "purpose"   = "homepage"
  }
  versioning    = false
  website       = {
    main_page_suffix = "index.html"
    not_found_page   = "404.html"
  }
  autoclass     = false
  cors          = [
    {
      max_age_seconds  = 3600
      method          = ["GET"]
      origin          = ["*.bodyscratch.academy"]
      response_header = []
    }
  ]
}

# Reserve an external IP for the load balancer
resource "google_compute_global_address" "bsc-homepage-external-ip" {
  name         = "bsc-homepage-global-external-ip"
  description  = "bodyscratch.academy (web) External IP"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV4"
  labels = {
    "bucket" = "homepage"
  }
}

resource "google_compute_global_address" "bsc-homepage-external-ipv6" {
  name         = "bsc-homepage-global-external-ipv6"
  description  = "bodyscratch.academy (web) External IPv6"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV6"
  labels = {
    "bucket" = "homepage"
  }
}

# Add the bucket as a CDN backend
resource "google_compute_backend_bucket" "bsc-homepage-backend-bucket" {
  name        = "bsc-homepage-backend-bucket"
  description = "bsc-homepage-backend-bucket"
  project     = local.project_id
  bucket_name = module.gcs_bsc-homepage.name
  enable_cdn  = true
  custom_response_headers = [
    "Strict-Transport-Security: max-age=31536000; includeSubDomains; preload",
    "X-Content-Type-Options: nosniff",
  ]
}

# Create HTTPS certificate
resource "google_compute_managed_ssl_certificate" "bsc-homepage-ssl-cert" {
  name          = "bsc-homepage-ssl-cert"
  description   = "SSL Cert for bodyscratch.academy (web)"
  project       = local.project_id
  managed {
    domains = ["bodyscratch.academy"]
  }
}

resource "google_compute_managed_ssl_certificate" "bsc-homepage-www-ssl-cert" {
  name          = "bsc-homepage-www-ssl-cert"
  description   = "SSL Cert for www.bodyscratch.academy"
  project       = local.project_id
  managed {
    domains = ["www.bodyscratch.academy"]
  }
}

# URL MAP
resource "google_compute_url_map" "bsc-homepage-url-map" {
  name            = "bsc-homepage-url-map"
  default_service = google_compute_backend_bucket.bsc-homepage-backend-bucket.self_link
  project         = local.project_id

  host_rule {
    hosts = [ "www.bodyscratch.academy" ]
    path_matcher = "www-redirect"
  }

  path_matcher {
    name = "www-redirect"
    default_url_redirect {
      host_redirect          = "bodyscratch.academy"
      https_redirect         = true
      redirect_response_code = "MOVED_PERMANENTLY_DEFAULT"
      strip_query           = false
    }
  }
}

# Target HTTPS Proxy
resource "google_compute_target_https_proxy" "bsc-homepage-target-proxy" {
  name             = "bsc-homepage-target-proxy"
  project          = local.project_id
  url_map          = google_compute_url_map.bsc-homepage-url-map.self_link
  ssl_certificates = [
    google_compute_managed_ssl_certificate.bsc-homepage-ssl-cert.self_link,
    google_compute_managed_ssl_certificate.bsc-homepage-www-ssl-cert.self_link
  ]
  ssl_policy       = data.google_compute_ssl_policy.ssl-policy.self_link
}

# Forwarding Rule
resource "google_compute_global_forwarding_rule" "bsc-homepage-forwarding-rule" {
  name                  = "bsc-homepage-forwarding-rule"
  project               = local.project_id
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.bsc-homepage-external-ip.address
  ip_protocol           = "TCP"
  port_range            = "443"
  target                = google_compute_target_https_proxy.bsc-homepage-target-proxy.self_link
}
resource "google_compute_global_forwarding_rule" "bsc-homepage-forwarding-rule-ipv6" {
  name                  = "bsc-homepage-forwarding-rule-ipv6"
  project               = local.project_id
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.bsc-homepage-external-ipv6.address
  ip_protocol           = "TCP"
  port_range            = "443"
  target                = google_compute_target_https_proxy.bsc-homepage-target-proxy.self_link
}