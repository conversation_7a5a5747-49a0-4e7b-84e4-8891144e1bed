## Label Studio
module "gcs-label-studio" {
  source        = "git::**************:BodyScratch/gcp-terraform-modules.git//cloud-storage?ref=main"
  project_id    = local.project_id
  name          = "bs-label-studio"
  location      = "EU"
  storage_class = "STANDARD"
  iam_members   = [
    {
      role      = "roles/storage.admin"
      member    = "serviceAccount:label-studio@${local.project_id}.iam.gserviceaccount.com"
    },
    {
      role      = "roles/storage.objectAdmin"
      member    = "serviceAccount:record-service-sa@${local.project_id}.iam.gserviceaccount.com"
    },
    {
      role      = "roles/storage.objectAdmin"
      member    = "serviceAccount:livekit-egress-sa@${local.project_id}.iam.gserviceaccount.com"
    },
    {
      role      = "roles/storage.admin"
      member    = "user:<EMAIL>"
    },
    {
      role      = "roles/storage.admin"
      member    = "user:<EMAIL>"
    }
  ]
  labels        = {
    "purpose"   = "label-studio"
  }
  versioning    = false
  autoclass     = true
  cors          = [
    {
      max_age_seconds  = 3600
      method          = ["GET"]
      origin          = ["https://labelstud.bodyscratch.academy"]
      response_header = ["Content-Type", "Access-Control-Allow-Origin"]
    }
  ]
}