# TODO: Move this VM to the DevOps Tools project

locals {
  vpn = {
    machine_type   = "e2-small"
    boot_disk_size = "15"
    boot_disk_type = "pd-standard"
  }
}

resource "google_service_account" "vpn" {
  project      = local.project_id
  account_id   = "${var.base_project_id}-vpn"
  display_name = "${var.base_project_id}-vpn"
}

resource "google_compute_instance" "vpn" {
  project      = local.project_id
  name         = "${var.base_project_id}-vpn"
  machine_type = local.vpn.machine_type
  zone         = "${var.region}-a"

  tags = ["vpn", "${var.base_project_id}-vpn"]

  boot_disk {
    initialize_params {
      image = "ubuntu-os-cloud/ubuntu-2404-lts-amd64"
      size  = local.vpn.boot_disk_size
      type  = local.vpn.boot_disk_type
      labels = {
        disk = "${var.base_project_id}-vpn"
      }
    }
  }

  network_interface {
    network    = data.google_compute_network.vpc-network.id
    subnetwork = data.google_compute_subnetwork.primary-subnetwork.id

    access_config {
      # External static IP
      nat_ip = "************"  # bodyscratch-prod-eu-europe-west4-vpn-external-ip
    }
  }

  can_ip_forward = true  # Required for VPN

  metadata = {
    instance = "${var.base_project_id}-vpn"
    "ssh-keys" = "trandatdt:ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIBDNmvtweKYdn1DqA1fpv7X59OtNbMpXa48ReIcpwHzj trandatdt"  # trandatdt's public key
  }

  service_account {
    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    email  = google_service_account.vpn.email
    scopes = ["https://www.googleapis.com/auth/logging.write", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/monitoring.write", "https://www.googleapis.com/auth/service.management.readonly", "https://www.googleapis.com/auth/servicecontrol", "https://www.googleapis.com/auth/trace.append"]
  }
}