variable "project_name" {
  description = "The name of the project the network resources are to be created in"
  type        = string
}

variable "base_project_id" {
  description = "The ID to give the project without the terraform suffix. If not provided, the `name` will be used."
  type        = string
  default     = ""
}

variable "region" {
  description = "The GCP region where the resources are to be provisioned"
  type        = string
}
