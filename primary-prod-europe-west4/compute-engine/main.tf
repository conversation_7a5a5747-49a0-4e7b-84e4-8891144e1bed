data "google_projects" "projects" {
  filter = "labels.project:${var.base_project_id}"
}

locals {
  project_id = data.google_projects.projects.projects[0].project_id
}

data "google_compute_network" "vpc-network" {
  project = local.project_id
  name    = "${var.base_project_id}-vpc"
}

data "google_compute_subnetwork" "primary-subnetwork" {
  project = local.project_id
  name    = "${var.base_project_id}-subnet-${var.region}"
  region  = var.region
}