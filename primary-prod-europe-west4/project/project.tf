module "project" {
  source                = "git::**************:BodyScratch/gcp-terraform-modules.git//project-factory/core_factory?ref=main"
  name                  = var.project_name
  project_id            = var.base_project_id
  org_id                = var.org_id
  billing_account       = var.billing_account
  folder_id             = var.folder_id
  create_project_sa     = var.create_project_sa
  project_sa_name       = var.project_sa_name
  sa_role               = var.project_sa_role
  activate_apis         = var.activate_apis
  random_project_id     = var.random_project_id
  create_project_sa_key = var.create_project_sa_key
  labels                = var.labels
}

module "project-iam-bindings" {
  source   = "git::**************:BodyScratch/gcp-terraform-modules.git//project-factory/iam?ref=main"
  projects = [module.project.project_id]
  mode     = "additive"

  bindings = var.project_iam_bindings

  depends_on = [module.project]
}
