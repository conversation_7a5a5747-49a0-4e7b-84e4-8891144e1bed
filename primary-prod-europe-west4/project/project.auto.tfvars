project_name          = "BodyScratch Pro --europe-west4"
base_project_id       = "bodyscratch-prod-eu"
region                = "europe-west4"
org_id                = "************"  // bodyscratch.academy
folder_id             = ""
billing_account       = "01A77E-7CCA16-20B60E"  // BSC-Wise
create_project_sa     = true
random_project_id     = true
create_project_sa_key = true
project_sa_name       = "terraform-bodyscratch-prod-eu"
project_sa_role       = "roles/owner"
activate_apis = [
  "dns.googleapis.com",
  "cloudresourcemanager.googleapis.com",
  "servicenetworking.googleapis.com",
  "compute.googleapis.com",
  "container.googleapis.com",
  "cloudkms.googleapis.com",
  "serviceusage.googleapis.com",
  "artifactregistry.googleapis.com",
  "secretmanager.googleapis.com",
  "file.googleapis.com",
  "sqladmin.googleapis.com",
  "certificatemanager.googleapis.com",
  "redis.googleapis.com"
]

labels = {
  environment = "prod-eu"
  project     = "bodyscratch-prod-eu"
}

project_iam_bindings = {
  "roles/owner" = [
    "user:<EMAIL>",
    "user:<EMAIL>",
  ],
  "roles/viewer" = [
    "user:<EMAIL>",
  ]
  "roles/pubsub.admin" = [
    "user:<EMAIL>",
  ],
  "roles/logging.viewer" = [
    "user:<EMAIL>",
  ]
}