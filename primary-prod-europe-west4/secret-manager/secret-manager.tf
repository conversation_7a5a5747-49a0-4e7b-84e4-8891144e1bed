## label-studio secrets
resource "google_secret_manager_secret" "label-studio-secrets" {
  project   = local.project_id
  secret_id = "label-studio-secrets"
  replication {
    auto {}
  }
  labels = {
    app = "label-studio"
    env = var.base_project_id
  }
}

## keycloak secrets
resource "google_secret_manager_secret" "keycloak-secrets" {
  project   = local.project_id
  secret_id = "keycloak-secrets"
  replication {
    auto {}
  }
  labels = {
    app = "keycloak"
    env = var.base_project_id
  }
}

## livekit secrets
resource "google_secret_manager_secret" "livekit-secrets" {
  project   = local.project_id
  secret_id = "livekit-secrets"
  replication {
    auto {}
  }
  labels = {
    app = "livekit"
    env = var.base_project_id
  }
}

## livekit-egress secrets
resource "google_secret_manager_secret" "livekit-egress-secrets" {
  project   = local.project_id
  secret_id = "livekit-egress-secrets"
  replication {
    auto {}
  }
  labels = {
    app = "livekit-egress"
    env = var.base_project_id
  }
}

## api-secrets
resource "google_secret_manager_secret" "api-secrets" {
  project   = local.project_id
  secret_id = "api-secrets"
  replication {
    auto {}
  }
  labels = {
    app = "api"
    env = var.base_project_id
  }
}

## ml-server
resource "google_secret_manager_secret" "ml-server-secrets" {
  project   = local.project_id
  secret_id = "ml-server-secrets"
  replication {
    auto {}
  }
  labels = {
    app = "ml-server"
    env = var.base_project_id
  }
}

## record-service
resource "google_secret_manager_secret" "record-service-secrets" {
  project   = local.project_id
  secret_id = "record-service-secrets"
  replication {
    auto {}
  }
  labels = {
    app = "record-service"
    env = var.base_project_id
  }
}

### RabbitMQ user secrets
## User `keda`
resource "google_secret_manager_secret" "rabbitmq-user-keda-secrets" {
  project   = local.project_id
  secret_id = "rabbitmq-user-keda-secrets"
  replication {
    auto {}
  }
  labels = {
    app = "keda"
    env = var.base_project_id
  }
}
## User `app`
resource "google_secret_manager_secret" "rabbitmq-user-app-secrets" {
  project   = local.project_id
  secret_id = "rabbitmq-user-app-secrets"
  replication {
    auto {}
  }
  labels = {
    app = "backend"
    env = var.base_project_id
  }
}