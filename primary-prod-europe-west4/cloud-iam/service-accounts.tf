locals {
    namespaces = {
        "external-secrets" = "external-secrets"
        "label-studio"     = "label-studio"
        "keda"             = "keda"
        "apis"             = "apis"
        "cert-manager"     = "cert-manager"
        "livekit"          = "livekit"
    }

    external-secrets-k8s-sa  = "external-secrets"
    label-studio-k8s-sa      = "label-studio"
    keda-k8s-sa              = "keda-operator"
    api-k8s-sa               = "api"
    record-service-k8s-sa    = "record-service"
    livekit-egress-k8s-sa    = "livekit-egress"
    cert-manager-sa          = "cert-manager"
}

## External Secrets SA
module "service_accounts-external-secrets" {
  source        = "git::**************:BodyScratch/gcp-terraform-modules.git//project-factory/service-accounts?ref=main"
  project_id    = local.project_id
  display_name  = "gke-external-secrets"
  names         = ["gke-external-secrets"]
  description   = "External Secrets SA"
  generate_keys = false
  project_roles = [
    "${local.project_id}=>roles/secretmanager.secretAccessor",
    "${local.project_id}=>roles/iam.serviceAccountTokenCreator"
  ]
}

resource "google_service_account_iam_binding" "external-secrets-sa" {
  service_account_id = module.service_accounts-external-secrets.service_account.name
  role               = "roles/iam.workloadIdentityUser"

  members = [
    "serviceAccount:${local.project_id}.svc.id.goog[${lookup(local.namespaces, "external-secrets")}/${local.external-secrets-k8s-sa}]",
  ]
  depends_on = [ module.service_accounts-external-secrets ]
}

## Label Studio SA
module "service_accounts-label-studio" {
  source        = "git::**************:BodyScratch/gcp-terraform-modules.git//project-factory/service-accounts?ref=main"
  project_id    = local.project_id
  display_name  = "label-studio"
  names         = ["label-studio"]
  description   = "Label Studio SA"
  generate_keys = false
  project_roles = [
    "${local.project_id}=>roles/iam.serviceAccountTokenCreator"
  ]
}

resource "google_service_account_iam_binding" "label-studio-sa" {
  service_account_id = module.service_accounts-label-studio.service_account.name
  role               = "roles/iam.workloadIdentityUser"

  members = [
    "serviceAccount:${local.project_id}.svc.id.goog[${lookup(local.namespaces, "label-studio")}/${local.label-studio-k8s-sa}-ls-app]",
  ]
  depends_on = [ module.service_accounts-label-studio ]
}

## KEDA SA
module "service_accounts-keda" {
  source        = "git::**************:BodyScratch/gcp-terraform-modules.git//project-factory/service-accounts?ref=main"
  project_id    = local.project_id
  display_name  = "keda-operator"
  names         = ["keda-operator"]
  description   = "KEDA SA"
  generate_keys = false

  project_roles = [
    "${local.project_id}=>roles/monitoring.viewer"
   ]
}

resource "google_service_account_iam_binding" "keda-sa" {
  service_account_id = module.service_accounts-keda.service_account.name
  role               = "roles/iam.workloadIdentityUser"

  members = [
    "serviceAccount:${local.project_id}.svc.id.goog[${lookup(local.namespaces, "keda")}/${local.keda-k8s-sa}]",
  ]
  depends_on = [ module.service_accounts-keda ]
}

## API SA
module "service_accounts-api" {
  source        = "git::**************:BodyScratch/gcp-terraform-modules.git//project-factory/service-accounts?ref=main"
  project_id    = local.project_id
  display_name  = "api-sa"
  names         = ["api-sa"]
  description   = "API SA"
  generate_keys = false

  project_roles = [
    "${local.project_id}=>roles/pubsub.publisher"
  ]
}

resource "google_service_account_iam_binding" "api-sa" {
  service_account_id = module.service_accounts-api.service_account.name
  role               = "roles/iam.workloadIdentityUser"

  members = [
    "serviceAccount:${local.project_id}.svc.id.goog[${lookup(local.namespaces, "apis")}/${local.api-k8s-sa}]",
  ]
  depends_on = [ module.service_accounts-api ]
}

## RECORD-SERVICE SA
module "service_accounts-record-service" {
  source        = "git::**************:BodyScratch/gcp-terraform-modules.git//project-factory/service-accounts?ref=main"
  project_id    = local.project_id
  display_name  = "record-service-sa"
  names         = ["record-service-sa"]
  description   = "RECORD-SERVICE SA"
  generate_keys = false
}

resource "google_service_account_iam_binding" "record-service-sa" {
  service_account_id = module.service_accounts-record-service.service_account.name
  role               = "roles/iam.workloadIdentityUser"

  members = [
    "serviceAccount:${local.project_id}.svc.id.goog[${lookup(local.namespaces, "apis")}/${local.record-service-k8s-sa}]",
  ]
  depends_on = [ module.service_accounts-record-service ]
}

## LIVEKIT-EGRESS SA
module "service_accounts-livekit-egress" {
  source        = "git::**************:BodyScratch/gcp-terraform-modules.git//project-factory/service-accounts?ref=main"
  project_id    = local.project_id
  display_name  = "livekit-egress-sa"
  names         = ["livekit-egress-sa"]
  description   = "LIVEKIT-EGRESS SA"
  generate_keys = false
}

resource "google_service_account_iam_binding" "livekit-egress-sa" {
  service_account_id = module.service_accounts-livekit-egress.service_account.name
  role               = "roles/iam.workloadIdentityUser"

  members = [
    "serviceAccount:${local.project_id}.svc.id.goog[${lookup(local.namespaces, "livekit")}/${local.livekit-egress-k8s-sa}]",
  ]
  depends_on = [ module.service_accounts-livekit-egress ]
}

## Cert Manager SA
module "service_accounts-cert-manager" {
  source        = "git::**************:BodyScratch/gcp-terraform-modules.git//project-factory/service-accounts?ref=main"
  project_id    = local.project_id
  display_name  = "cert-manager"
  names         = ["cert-manager"]
  description   = "Cert Manager SA for DNS01 challenge"
  generate_keys = false
  project_roles = [
    "${local.project_id}=>roles/dns.admin"
  ]
}

resource "google_service_account_iam_binding" "cert-manager-sa" {
  service_account_id = module.service_accounts-cert-manager.service_account.name
  role               = "roles/iam.workloadIdentityUser"

  members = [
    "serviceAccount:${local.project_id}.svc.id.goog[${lookup(local.namespaces, "cert-manager")}/${local.cert-manager-sa}]",
  ]
  depends_on = [ module.service_accounts-cert-manager ]
}