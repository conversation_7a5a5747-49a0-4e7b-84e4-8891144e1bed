ENVIRONMENT ?= BodyScratchProd
PROJECT_NAME ?= bodyscratch-prod-eu
REGION ?= europe-west4

BUCKET_PREFIX := $(ENVIRONMENT)/$(PROJECT_NAME)/cloud-iam
LOCAL_SECRET_DIR := .secrets

define tfvars
region = "$(REGION)"
endef
export tfvars

export GOOGLE_APPLICATION_CREDENTIALS=${LOCAL_SECRET_DIR}/terraform-admin-sa.json
export GCS_SA_KEY = ${LOCAL_SECRET_DIR}/terraform-admin-sa.json
export STATE_FILE = ${PROJECT_NAME}

.secrets:
	@mkdir -p $@
	@gcloud secrets versions access 1 --secret="terraform-admin-sa" --project="bodyscratch-devops-tools" > $@/terraform-admin-sa.json

terraform.tfvars: .secrets
	@cat $^ > $@ 2>/dev/null || :
	@echo "$$tfvars" >> $@

.terraform:
	@terraform init \
		-backend=true \
		-backend-config="prefix=$(BUCKET_PREFIX)" \
		-backend-config="credentials=$(GCS_SA_KEY)" \
			$<
	@touch $@
	@terraform workspace select ${STATE_FILE} || terraform workspace new ${STATE_FILE}

.login:
	# TODO: Implement Vault login
	@echo "TODO: Implement login"

clean:
	-rm -f terraform.tfvars
	-rm -f .terraform.lock.hcl
	-rm -rf .terraform
	-rm -rf .secrets

clean-secrets:
	-rm -rf .secrets

# Enforces terraform format on rendered output
lint:
	@terraform fmt -check -diff -no-color .

validate: terraform.tfvars .terraform
	@terraform validate .
	@make clean-secrets

plan: .login terraform.tfvars .terraform
	@terraform plan
	#@make clean-secrets

apply: .login terraform.tfvars .terraform
	@terraform apply -auto-approve=true
	@make clean-secrets

destroy: terraform.tfvars .terraform
	@terraform destroy
	@make clean-secrets

import: terraform.tfvars .terraform
	@terraform import $(resource_name) $(id)
	@make clean-secrets

state-list: terraform.tfvars .terraform
	@terraform state list
	@make clean-secrets

revert-import:
	@terraform state rm $(resource_name)

.PHONY: clean lint plan apply destroy login