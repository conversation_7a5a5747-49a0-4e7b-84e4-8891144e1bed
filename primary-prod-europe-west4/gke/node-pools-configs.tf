locals {
  gke_node_pools_config = {
    "apps-critical" = {
      name                             = "apps-critical"
      autoscaling                      = true
      image_type                       = var.image_type
      machine_type                     = "e2-standard-4"
      disk_size_gb                     = 50
      disk_type                        = "pd-ssd"
      min_count                        = 1
      max_count                        = 1
      initial_node_count               = 1
      total_min_count                  = 1
      total_max_count                  = 3
      boot_disk_kms_key                = "projects/${local.project_id}/locations/${var.region}/keyRings/${var.base_project_id}-keys/cryptoKeys/GKE-bootdisk"
      enable_integrity_monitoring      = true
      node_pools_create_before_destroy = true
      auto_repair                      = true
      auto_upgrade                     = false
      preemptible                      = false
      enable_secure_boot               = true
      enable_gcfs                      = true
      spot                             = false
      node_locations                   = "${var.region}-a,${var.region}-b,${var.region}-c"
      location_policy                  = "BALANCED"
      version                          = var.kubernetes_version
    }

    "apps-internal-tools" = {
      name                             = "apps-internal-tools"
      autoscaling                      = true
      image_type                       = var.image_type
      machine_type                     = "e2-standard-8"
      disk_size_gb                     = 50
      disk_type                        = "pd-ssd"
      min_count                        = 1
      max_count                        = 1
      initial_node_count               = 1
      total_min_count                  = 1
      total_max_count                  = 1
      boot_disk_kms_key                = "projects/${local.project_id}/locations/${var.region}/keyRings/${var.base_project_id}-keys/cryptoKeys/GKE-bootdisk"
      enable_integrity_monitoring      = true
      node_pools_create_before_destroy = true
      auto_repair                      = true
      auto_upgrade                     = false
      preemptible                      = false
      enable_secure_boot               = true
      enable_gcfs                      = true
      spot                             = true
      node_locations                   = "${var.region}-a"
      location_policy                  = "BALANCED"
      version                          = var.kubernetes_version
    }

    "apps-livekit" = {
      name                             = "apps-livekit"
      autoscaling                      = true
      image_type                       = var.image_type
      machine_type                     = "e2-standard-4"
      disk_size_gb                     = 50
      disk_type                        = "pd-ssd"
      min_count                        = 1
      max_count                        = 1
      initial_node_count               = 1
      total_min_count                  = 1
      total_max_count                  = 1
      boot_disk_kms_key                = "projects/${local.project_id}/locations/${var.region}/keyRings/${var.base_project_id}-keys/cryptoKeys/GKE-bootdisk"
      enable_integrity_monitoring      = true
      node_pools_create_before_destroy = true
      auto_repair                      = true
      auto_upgrade                     = false
      preemptible                      = false
      enable_secure_boot               = true
      enable_gcfs                      = true
      spot                             = true
      node_locations                   = "${var.region}-a"
      location_policy                  = "BALANCED"
      version                          = var.kubernetes_version
      enable_private_nodes             = false  # NOTE: Put here but it has no effect because the module is not using it. Need to be set manually on the Google console.
    }

    "apps-livekit-egress" = {
      name                             = "apps-livekit-egress"
      autoscaling                      = true
      image_type                       = var.image_type
      machine_type                     = "c3-highcpu-4"
      disk_size_gb                     = 50
      disk_type                        = "pd-ssd"
      min_count                        = 1
      max_count                        = 1
      initial_node_count               = 1
      total_min_count                  = 1
      total_max_count                  = 1
      boot_disk_kms_key                = "projects/${local.project_id}/locations/${var.region}/keyRings/${var.base_project_id}-keys/cryptoKeys/GKE-bootdisk"
      enable_integrity_monitoring      = true
      node_pools_create_before_destroy = true
      auto_repair                      = true
      auto_upgrade                     = false
      preemptible                      = false
      enable_secure_boot               = true
      enable_gcfs                      = true
      spot                             = true
      node_locations                   = "${var.region}-a"
      location_policy                  = "BALANCED"
      version                          = var.kubernetes_version
    }

    "apps-backend" = {
      name                             = "apps-backend"
      autoscaling                      = true
      image_type                       = var.image_type
      machine_type                     = "e2-standard-4"
      disk_size_gb                     = 25
      disk_type                        = "pd-ssd"
      min_count                        = 1
      max_count                        = 1
      total_min_count                  = 1
      total_max_count                  = 3
      initial_node_count               = 1
      boot_disk_kms_key                = "projects/${local.project_id}/locations/${var.region}/keyRings/${var.base_project_id}-keys/cryptoKeys/GKE-bootdisk"
      enable_integrity_monitoring      = true
      node_pools_create_before_destroy = true
      auto_repair                      = true
      auto_upgrade                     = false
      preemptible                      = false
      enable_secure_boot               = true
      enable_gcfs                      = true
      spot                             = false
      node_locations                   = "${var.region}-a,${var.region}-b,${var.region}-c"
      location_policy                  = "BALANCED"
      version                          = var.kubernetes_version
    }

    "apps-ml-server" = {
      name                             = "apps-ml-server"
      autoscaling                      = true
      image_type                       = var.image_type
      machine_type                     = "c4-standard-4"
      disk_size_gb                     = 50
      disk_type                        = "hyperdisk-balanced"
      min_count                        = 1
      max_count                        = 10
      total_min_count                  = 1
      total_max_count                  = 30
      initial_node_count               = 1
      boot_disk_kms_key                = "projects/${local.project_id}/locations/${var.region}/keyRings/${var.base_project_id}-keys/cryptoKeys/GKE-bootdisk"
      enable_integrity_monitoring      = true
      node_pools_create_before_destroy = true
      auto_repair                      = true
      auto_upgrade                     = false
      preemptible                      = false
      enable_secure_boot               = true
      enable_gcfs                      = true
      spot                             = true
      node_locations                   = "${var.region}-a,${var.region}-b,${var.region}-c"
      location_policy                  = "BALANCED"
      version                          = var.kubernetes_version
    }

    "apps-record-service" = {
      name                             = "apps-record-service"
      autoscaling                      = true
      image_type                       = var.image_type
      machine_type                     = "c4a-standard-2"
      disk_size_gb                     = 50
      disk_type                        = "hyperdisk-balanced"
      min_count                        = 1
      max_count                        = 1
      total_min_count                  = 1
      total_max_count                  = 1
      initial_node_count               = 1
      boot_disk_kms_key                = "projects/${local.project_id}/locations/${var.region}/keyRings/${var.base_project_id}-keys/cryptoKeys/GKE-bootdisk"
      enable_integrity_monitoring      = true
      node_pools_create_before_destroy = true
      auto_repair                      = true
      auto_upgrade                     = false
      preemptible                      = false
      enable_secure_boot               = true
      enable_gcfs                      = true
      spot                             = false
      node_locations                   = "${var.region}-a,${var.region}-b,${var.region}-c"
      location_policy                  = "BALANCED"
      version                          = var.kubernetes_version
    }

    "apps-prelabel-service" = {
      name                             = "apps-prelabel-service"
      autoscaling                      = true
      image_type                       = var.image_type
      machine_type                     = "c4-standard-4"
      disk_size_gb                     = 200
      disk_type                        = "hyperdisk-balanced"
      min_count                        = 1
      max_count                        = 1
      total_min_count                  = 1
      total_max_count                  = 1
      initial_node_count               = 1
      boot_disk_kms_key                = "projects/${local.project_id}/locations/${var.region}/keyRings/${var.base_project_id}-keys/cryptoKeys/GKE-bootdisk"
      enable_integrity_monitoring      = true
      node_pools_create_before_destroy = true
      auto_repair                      = true
      auto_upgrade                     = false
      preemptible                      = false
      enable_secure_boot               = true
      enable_gcfs                      = true
      spot                             = false
      node_locations                   = "${var.region}-a,${var.region}-b,${var.region}-c"
      location_policy                  = "BALANCED"
      version                          = var.kubernetes_version
    }
  }
}