module "gke" {
  source = "git::**************:BodyScratch/gcp-terraform-modules.git//google-kubernetes-engine/modules/beta-private-cluster-update-variant?ref=main"

  project_id                  = local.project_id
  name                        = "${var.base_project_id}-gke-cluster"
  description                 = "BodyScratch Prod GKE cluster in ${var.region}"
  kubernetes_version          = var.kubernetes_version
  release_channel             = var.release_channel
  regional                    = true
  region                      = var.region
  network                     = local.network_name
  subnetwork                  = local.main_subnet_name
  ip_range_pods               = "${var.base_project_id}-pods-subnet"
  ip_range_services           = "${var.base_project_id}-svc-subnet"
  enable_private_endpoint     = var.private_api
  enable_private_nodes        = true
  master_ipv4_cidr_block      = var.master_ipv4_cidr_block
  grant_registry_access       = true
  registry_project_ids        = var.registry_project_ids
  datapath_provider           = "ADVANCED_DATAPATH"
  enable_binary_authorization = false
  #enable_image_streaming               = true
  enable_confidential_nodes            = false
  enable_vertical_pod_autoscaling      = true
  filestore_csi_driver                 = true
  dns_cache                            = true
  enable_intranode_visibility          = true
  gce_pd_csi_driver                    = true
  enable_cost_allocation               = true
  gcs_fuse_csi_driver                  = true
  gke_backup_agent_config              = true
  monitoring_enable_managed_prometheus = false
  #logging_enabled_components           = ["SYSTEM_COMPONENTS"]
  monitoring_enabled_components = ["SYSTEM_COMPONENTS"]
  cluster_dns_provider          = "CLOUD_DNS"
  cluster_dns_scope             = "CLUSTER_SCOPE"

  master_authorized_networks   = var.master_authorized_networks
  master_global_access_enabled = true
  maintenance_start_time       = "1970-01-01T06:00:00Z"
  maintenance_end_time         = "1970-01-01T10:00:00Z"
  maintenance_recurrence       = "FREQ=WEEKLY;BYDAY=FR,SA,SU"
  maintenance_exclusions = [
  ]
  enable_gcfs = true
  cluster_autoscaling = {
    enabled             = false
    autoscaling_profile = "BALANCED"
    max_cpu_cores       = 0
    min_cpu_cores       = 0
    max_memory_gb       = 0
    min_memory_gb       = 0
    gpu_resources       = []
    auto_repair         = true
    auto_upgrade        = false
  }

  enable_network_egress_export = false
  ## Application-layer secrets encryption
  database_encryption = [
    {
      key_name = "projects/${local.project_id}/locations/${var.region}/keyRings/${var.base_project_id}-keys/cryptoKeys/GKE-secrets"
      state    = "ENCRYPTED"
    }
  ]

  cluster_resource_labels = {
    cluster-name = "${var.base_project_id}-gke-cluster"
    environment  = "prod"
    region       = "europe-west4"
  }

  remove_default_node_pool = true
  node_pools = [
    lookup(local.gke_node_pools_config, "apps-critical"), ## KEDA, Kube-DNS and etc
    lookup(local.gke_node_pools_config, "apps-internal-tools"), ## Label Studio, JupyterHub and etc
    lookup(local.gke_node_pools_config, "apps-livekit"), ## LiveKit
    lookup(local.gke_node_pools_config, "apps-livekit-egress"), ## LiveKit-EGRESS
    lookup(local.gke_node_pools_config, "apps-backend"), ## Backend
    lookup(local.gke_node_pools_config, "apps-ml-server"), ## ML Server
    lookup(local.gke_node_pools_config, "apps-record-service"), ## Record Service
    lookup(local.gke_node_pools_config, "apps-prelabel-service"), ## Prelabel Service
  ]

  node_pools_labels = {
    apps-critical = {
      apps = "critical"
    }

    apps-internal-tools = {
      apps = "internal_tools"
    }

    apps-livekit = {
      apps = "livekit"
    }

    apps-livekit-egress = {
      apps = "livekit-egress"
    }

    apps-backend = {
      apps = "backend"
    }

    apps-ml-server = {
      apps = "ml-server"
    }

    apps-record-service = {
      apps = "record-service"
    }

    apps-prelabel-service = {
      apps = "prelabel-service"
    }
  }

  node_pools_tags = {
    apps-livekit = [
      "livekit-server"
    ]
  }

  node_pools_taints = {
    apps-internal-tools = [{
      key    = "apps"
      value  = "internal_tools"
      effect = "NO_SCHEDULE"
    }]

    app-livekit = [{
      key    = "apps"
      value  = "livekit"
      effect = "NO_SCHEDULE"
    }]

    apps-livekit-egress = [{
      key    = "apps"
      value  = "livekit-egress"
      effect = "NO_SCHEDULE"
    }]

    apps-backend = [{
      key    = "apps"
      value  = "backend"
      effect = "NO_SCHEDULE"
    }]

    apps-ml-server = [{
      key    = "apps"
      value  = "ml-server"
      effect = "NO_SCHEDULE"
    }]

    apps-record-service = [{
      key    = "apps"
      value  = "record-service"
      effect = "NO_SCHEDULE"
    }]

    apps-prelabel-service = [{
      key    = "apps"
      value  = "prelabel-service"
      effect = "NO_SCHEDULE"
    }]
  }
}