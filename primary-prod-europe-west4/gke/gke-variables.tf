variable "project_name" {
  description = "The name of the project the network resources are to be created in"
  type        = string
}

variable "base_project_id" {
  description = "The ID to give the project without the terraform suffix. If not provided, the `name` will be used."
  type        = string
  default     = ""
}

variable "region" {
  description = "The GCP region where the resources are to be provisioned"
  type        = string
}

variable "release_channel" {
  default = "REGULAR"
}

variable "min_count" {
  description = "Minimum number of Kubernetes nodes in the pool"
  default     = 1
}

variable "max_count" {
  description = "Maximum number of Kubernetes nodes in the pool"
  default     = 3
}
variable "machine_type" {
  default = "n1-highmem-4"
}

variable "disk_type" {
  default = "pd-standard"
}

variable "disk_size" {
  default = 100
}

variable "vault_secrets" {
  description = "A list of secrets from Vault to provision as Kubernetes secrets"
  default     = []
}

variable "master_ipv4_cidr_block" {
  type        = string
  description = "(Beta) The IP range in CIDR notation to use for the hosted master network"
  default     = "10.0.0.0/28"
}

variable "private_api" {
  description = "Boolean to determine if the K8S API should be private"
  default     = true
}

variable "master_authorized_networks" {
  type        = list(any)
  description = "Authorized Master authorized networks IP CIDR block"
  default     = []
}

variable "cloud_armor_allowed_ip_ranges" {
  default = ["*"]
}

variable "cloud_armor_allowed_service_ip_ranges" {
  default = ["*"]
}

variable "create_avfim_daemonset" {
  description = "Boolean to determine if we should deploy the AV and FIM daemonset"
  default     = false
}

variable "topic" {
  description = "The name of the Pub/Sub Topic and Subscription"
  default     = ""
}

variable "resource_usage_export_dataset_id" {
  type        = string
  description = "The ID of a BigQuery Dataset for using BigQuery as the destination of resource usage export."
  default     = ""
}

variable "kubernetes_version" {
  type        = string
  description = "The Kubernetes version of the masters. If set to 'latest' it will pull latest available version in the selected region."
  default     = "latest"
}

variable "datapath_provider" {
  type        = string
  description = "The desired datapath provider for this cluster. By default, `DATAPATH_PROVIDER_UNSPECIFIED` enables the IPTables-based kube-proxy implementation. `ADVANCED_DATAPATH` enables Dataplane-V2 feature."
  default     = "DATAPATH_PROVIDER_UNSPECIFIED"
}

variable "enable_binary_authorization" {
  description = "Enable BinAuthZ Admission controller"
  default     = false
}

variable "initial_node_count" {
  type        = number
  description = "The number of nodes to create in this cluster's default node pool."
  default     = 0
}

variable "registry_project_ids" {
  type        = list(string)
  description = "Projects holding Google Container Registries. If empty, we use the cluster project. If a service account is created and the `grant_registry_access` variable is set to `true`, the `storage.objectViewer` and `artifactregsitry.reader` roles are assigned on these projects."
  default     = []
}

variable "grant_registry_access" {
  type        = bool
  description = "Grants created cluster-specific service account storage.objectViewer role."
  default     = false
}

variable "image_type" {
  type        = string
  description = "The image type to use for this node. Note that changing the image type will delete and recreate all nodes in the node pool"
  default     = "COS_CONTAINERD"
}

variable "registry_location" {
  description = "The location where the artifact registry to be created. It can point to a specific region or multi-region (ex: asia, europe or us)"
  default     = "us-east1"
}