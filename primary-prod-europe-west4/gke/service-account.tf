# module "project-iam-bindings-gke" {
#   source   = "git::**************:BodyScratch/gcp-terraform-modules.git//project-factory/iam?ref=main"
#   projects = [local.project_id]
#   mode     = "additive"

#   bindings = {
#     "roles/iam.workloadIdentityUser" = [
#       "serviceAccount:${module.gke.service_account}"
#     ]
#     "roles/stackdriver.resourceMetadata.writer" = [
#       "serviceAccount:${module.gke.service_account}"
#     ]
#     "roles/artifactregistry.reader" = [
#       "serviceAccount:${module.gke.service_account}"
#     ]
#   }

#   depends_on = [module.gke]
# }