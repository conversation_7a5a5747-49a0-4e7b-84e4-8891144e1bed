variable "project_name" {
  description = "The name of the project where the CloudKMS resources are to be created in"
  type        = string
}

variable "base_project_id" {
  description = "The ID to give the project without the terraform suffix. If not provided, the `name` will be used."
  type        = string
  default     = ""
}

variable "region" {
  description = "The GCP region where the resources are to be provisioned"
  type        = string
}

variable "keys" {
  description = "Key names."
  type        = list(string)
  default     = []
}

variable "key_protection_level" {
  type        = string
  description = "The protection level to use when creating a version based on this template. Default value: \"SOFTWARE\" Possible values: [\"SOFTWARE\", \"HSM\"]"
  default     = "SOFTWARE"
}
