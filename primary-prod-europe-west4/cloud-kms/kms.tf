# GKE secrets and boot disk encryption keys
module "kms" {
  source = "git::**************:BodyScratch/gcp-terraform-modules.git//cloud-kms?ref=main"

  project_id           = local.project_id
  keyring              = "${var.base_project_id}-keys"
  location             = var.region
  keys                 = var.keys
  key_rotation_period  = ""
  key_protection_level = var.key_protection_level
  set_decrypters_for   = var.keys
  set_encrypters_for   = var.keys

  decrypters = [
    "serviceAccount:service-${local.project_number}@container-engine-robot.iam.gserviceaccount.com",
    "serviceAccount:service-${local.project_number}@compute-system.iam.gserviceaccount.com",
  ]
  encrypters = [
    "serviceAccount:service-${local.project_number}@container-engine-robot.iam.gserviceaccount.com",
    "serviceAccount:service-${local.project_number}@compute-system.iam.gserviceaccount.com",
  ]

  # Terraform can't create the service account for CloudSQL identity
  # https://cloud.google.com/sql/docs/mysql/configure-cmek#service-account
  # gcloud beta services identity create --service=sqladmin.googleapis.com --project=[USER_PROJECT]

  # keys can be destroyed by Terraform
  prevent_destroy = false
}

# Cloud SQL crypto key
resource "google_project_service_identity" "gcp_sa_cloud_sql" {
  service  = "sqladmin.googleapis.com"
  provider = google-beta
  project = local.project_id
}

resource "google_kms_crypto_key" "cloud_sql_crypto_key" {
  name             = "cloud-sql-crypto-key"
  key_ring         = module.kms.keyring
  purpose          = "ENCRYPT_DECRYPT"
  rotation_period  = "2592000s" # 30 days

  version_template {
    algorithm = "GOOGLE_SYMMETRIC_ENCRYPTION"
    protection_level = "HSM"
  }
}
# Grant CloudSQL Service Account Access to Use the Key
resource "google_kms_crypto_key_iam_member" "cloudsql_key_permissions" {
  crypto_key_id = google_kms_crypto_key.cloud_sql_crypto_key.id
  role          = "roles/cloudkms.cryptoKeyEncrypterDecrypter"
  member        = "serviceAccount:${google_project_service_identity.gcp_sa_cloud_sql.email}"
}