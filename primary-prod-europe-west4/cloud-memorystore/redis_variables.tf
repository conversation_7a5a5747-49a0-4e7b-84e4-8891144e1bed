variable "project_name" {
  description = "The name of the project the network resources are to be created in"
  type        = string
}

variable "base_project_id" {
  description = "The ID to give the project without the terraform suffix. If not provided, the `name` will be used."
  type        = string
  default     = ""
}

variable "region" {
  description = "The GCP region where the resources are to be provisioned"
  type        = string
}

variable "tier" {
  description = "The Memorystore redis tier"
  type        = string
  default     = "BASIC"
}

variable "memory_size_gb_core" {
  description = "Core CI/CD Redis memory size in GiB. Defaulted to 1 GiB"
  type        = number
  default     = "1"
}

variable "memory_size_gb_dds" {
  description = "Data Deploy Service Redis memory size in GiB. Defaulted to 1 GiB"
  type        = number
  default     = "1"
}

variable "core_redis_version" {
  description = "The version of copacloud-core Redis software."
  type        = string
  default     = null
}

variable "data_deploy_redis_version" {
  description = "The version of data_deploy Redis software."
  type        = string
  default     = null
}

variable "connect_mode" {
  description = "The connection mode of the Redis instance. Can be either DIRECT_PEERING or PRIVATE_SERVICE_ACCESS. The default connect mode if not provided is DIRECT_PEERING."
  type        = string
  default     = null
}