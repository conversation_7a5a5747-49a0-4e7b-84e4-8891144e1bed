module "memorystore_core_redis" {
  source = "git::**************:BodyScratch/gcp-terraform-modules.git//cloud-memorystore?ref=main"

  name               = "${var.base_project_id}-redis"
  project            = local.project_id
  tier               = var.tier
  memory_size_gb     = var.memory_size_gb_core
  region             = var.region
  connect_mode       = var.connect_mode
  authorized_network = local.network_id
  auth_enabled       = true
  redis_version      = var.core_redis_version
  redis_configs = {
    maxmemory-policy = "allkeys-lru"
    maxmemory-gb     = "0.9"
  }

  maintenance_policy = {
    day = "SUNDAY"
    start_time = {
      hours   = 7
      minutes = 0
      nanos   = 0
      seconds = 0
    }
  }

  persistence_config = {
    persistence_mode        = "RDB"
    rdb_snapshot_period     = "TWENTY_FOUR_HOURS"
    rdb_next_snapshot_time  = ""
    rdb_snapshot_start_time = ""
  }

}