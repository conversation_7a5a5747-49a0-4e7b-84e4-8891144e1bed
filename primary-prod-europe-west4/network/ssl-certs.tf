## SSL Cert for Label Studio
resource "google_compute_managed_ssl_certificate" "label-studio-ssl-cert" {
  name        = "label-studio-ssl-cert"
  description = "SSL Cert for label-studio"
  project     = local.project_id
  managed {
    domains = ["labelstud.bodyscratch.academy"]
  }
}

## SSL Cert for Keycloak
resource "google_compute_managed_ssl_certificate" "keycloak-ssl-cert" {
  name        = "keycloak-ssl-cert"
  description = "SSL Cert for Keycloak"
  project     = local.project_id
  managed {
    domains = ["keycloak.bodyscratch.academy"]
  }
}

## SSL Cert for Livekit
resource "google_compute_managed_ssl_certificate" "livekit-ssl-cert" {
  name        = "livekit-ssl-cert"
  description = "SSL Cert for Livekit"
  project     = local.project_id
  managed {
    domains = ["live.bodyscratch.academy"]
  }
}

## SSL Cert for API
resource "google_compute_managed_ssl_certificate" "api-ssl-cert" {
  name        = "api-ssl-cert"
  description = "SSL Cert for api.bodyscratch.academy"
  project     = local.project_id
  managed {
    domains = ["api.bodyscratch.academy"]
  }
}

## SSL Cert for app.bodyscratch.academy
resource "google_compute_managed_ssl_certificate" "app-web-ssl-cert" {
  name        = "app-web-ssl-cert"
  description = "SSL Cert for app.bodyscratch.academy"
  project     = local.project_id
  managed {
    domains = ["app.bodyscratch.academy"]
  }
}

## SSL Cert for play.bodyscratch.academy
resource "google_compute_managed_ssl_certificate" "play-web-ssl-cert" {
  name        = "play-web-ssl-cert"
  description = "SSL Cert for play.bodyscratch.academy"
  project     = local.project_id
  managed {
    domains = ["play.bodyscratch.academy"]
  }
}

## SSL Cert for record-app.bodyscratch.academy
resource "google_compute_managed_ssl_certificate" "record-app-web-ssl-cert" {
  name        = "record-app-web-ssl-cert"
  description = "SSL Cert for record-app.bodyscratch.academy"
  project     = local.project_id
  managed {
    domains = ["record-app.bodyscratch.academy"]
  }
}

## SSL Cert for record-control-app.bodyscratch.academy
resource "google_compute_managed_ssl_certificate" "record-control-app-web-ssl-cert" {
  name        = "record-control-app-web-ssl-cert"
  description = "SSL Cert for record-control-app.bodyscratch.academy"
  project     = local.project_id
  managed {
    domains = ["record-control-app.bodyscratch.academy"]
  }
}