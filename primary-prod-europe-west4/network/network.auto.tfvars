project_name    = "BodyScratch Pro --europe-west4"
base_project_id = "bodyscratch-prod-eu"
region          = "europe-west4"
subnets = [
  {
    subnet_name               = "bodyscratch-prod-eu-subnet-europe-west4"
    subnet_ip                 = "**********/22"
    subnet_region             = "europe-west4"
    subnet_private_access     = "true"
    subnet_flow_logs          = "false"
    subnet_flow_logs_interval = "INTERVAL_10_MIN"
    subnet_flow_logs_sampling = 0.7
    subnet_flow_logs_metadata = "INCLUDE_ALL_METADATA"
  },
]

secondary_ranges = {
  "bodyscratch-prod-eu-subnet-europe-west4" = [
    {
      range_name    = "bodyscratch-prod-eu-pods-subnet"
      ip_cidr_range = "********/15"
    },
    {
      range_name    = "bodyscratch-prod-eu-svc-subnet"
      ip_cidr_range = "*************/19"
    },
  ],
}

source_ip_ranges_to_nat = ["ALL_IP_RANGES"]
vpc_peering_ip          = "**********"