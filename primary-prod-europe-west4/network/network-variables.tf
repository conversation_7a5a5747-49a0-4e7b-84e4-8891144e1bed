/*
**************************************************
Network variables
**************************************************
*/

variable "project_name" {
  description = "The name of the project the network resources are to be created in"
  type        = string
}

variable "base_project_id" {
  description = "The ID to give the project without the terraform suffix. If not provided, the `name` will be used."
  type        = string
  default     = ""
}

variable "subnets" {
  description = "Subnet definitions"
}

variable "secondary_ranges" {
  description = "Subnet secondary IP ranges"
  default     = {}
}

variable "region" {
  description = "The GCP region where the resources are to be provisioned"
  type        = string
}

variable "ip_names" {
  description = "List of names to create IP addresses for"
  default     = []
}


variable "source_ip_ranges_to_nat" {
  type        = list(string)
  description = "Primary IP range of the subnet to map to the NAT gateway"
  default     = []
}

variable "vpc_peering_ip" {
  type        = string
  description = "The IP address start range for private VPC connection with a GCP service provider"
}