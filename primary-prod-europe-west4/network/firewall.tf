## Livekit Server firewall rules
## https://docs.livekit.io/home/<USER>/ports-firewall/
module "allow-ingress-all-ipv4-livekit-server-ports" {
  source       = "git::**************:BodyScratch/gcp-terraform-modules.git//network/modules/firewall-rules?ref=main"
  project_id   = local.project_id
  network_name = "${var.base_project_id}-vpc"

  ingress_rules = [{
    name                    = "allow-ingress-all-ipv4-livekit-server-ports"
    description             = "allow-ingress-all-ipv4-livekit-server-ports"
    source_ranges           = ["0.0.0.0/0"]
    target_tags             = ["livekit-server"]
    allow = [
      {
        protocol = "tcp"
        ports    = ["7881"]
      },
      {
        protocol = "udp"
        ports    = ["50000-60000"]
      }
    ]
    deny = []
  }]
  egress_rules = null
  rules = null
}

module "allow-ingress-all-ipv6-livekit-server-ports" {
  source       = "git::**************:BodyScratch/gcp-terraform-modules.git//network/modules/firewall-rules?ref=main"
  project_id   = local.project_id
  network_name = "${var.base_project_id}-vpc"

  ingress_rules = [{
    name                    = "allow-ingress-all-ipv6-livekit-server-ports"
    description             = "allow-ingress-all-ipv6-livekit-server-ports"
    source_ranges           = ["::/0"]
    target_tags             = ["livekit-server"]
    allow = [
      {
        protocol = "tcp"
        ports    = ["7881"]
      },
      {
        protocol = "udp"
        ports    = ["50000-60000"]
      }
    ]
    deny = []
  }]
  egress_rules = null
  rules = null
}