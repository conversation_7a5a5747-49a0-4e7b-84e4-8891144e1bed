
module "cloud-nat" {
  source = "git::**************:BodyScratch/gcp-terraform-modules.git//network/modules/cloud-nat?ref=main"

  project_id = local.project_id
  region     = var.region
  name       = "${var.base_project_id}-nat"

  create_router = true
  router        = "${var.base_project_id}-nat-router"
  network       = "${var.base_project_id}-vpc"
  router_asn    = "64514"

  enable_dynamic_port_allocation      = true
  enable_endpoint_independent_mapping = false
  min_ports_per_vm                    = 2048
  max_ports_per_vm                    = 65536
  nat_ips                             = [google_compute_address.bodyscratch-prod-eu-cldnat-ip-1.self_link, google_compute_address.bodyscratch-prod-eu-cldnat-ip-2.self_link, google_compute_address.bodyscratch-prod-eu-cldnat-ip-3.self_link]
  tcp_established_idle_timeout_sec    = 2400
  tcp_transitory_idle_timeout_sec     = 120

  #source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  source_subnetwork_ip_ranges_to_nat = "LIST_OF_SUBNETWORKS"
  subnetworks = [
    {
      name                     = "${var.base_project_id}-subnet-europe-west4"
      source_ip_ranges_to_nat  = var.source_ip_ranges_to_nat
      secondary_ip_range_names = ["${var.base_project_id}-pods-subnet", "${var.base_project_id}-svc-subnet"]
    }
  ]

  log_config_enable = "true"
  log_config_filter = "ERRORS_ONLY"

  depends_on = [module.vpc]
}
