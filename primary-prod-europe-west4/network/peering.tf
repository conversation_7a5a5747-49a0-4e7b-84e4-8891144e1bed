# Private services access is a private connection between your VPC network and a network owned by Google or a third party.
# Google or the third party, entities who are offering services, are also known as service producers.
# The private connection enables VM instances in your VPC network and the services that you access to communicate exclusively by using internal IP addresses.
## https://cloud.google.com/vpc/docs/configure-private-services-access

# https://cloud.google.com/vpc/docs/configure-private-services-access#allocating-range
resource "google_compute_global_address" "vpc_peering_ip" {
  name          = "${var.base_project_id}-vpc-peering-ip"
  network       = module.vpc.network_id
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 16
  address       = var.vpc_peering_ip
  project       = local.project_id

  depends_on = [module.vpc]
}

# Private VPC connection with a GCP service provider
resource "google_service_networking_connection" "private_vpc_connection" {
  network                 = module.vpc.network_id
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.vpc_peering_ip.name]

  depends_on = [module.vpc, google_compute_global_address.vpc_peering_ip]
}

resource "google_compute_network_peering_routes_config" "private_service_access_generic" {
  peering = "servicenetworking-googleapis-com"
  network = module.vpc.network_name
  project = local.project_id

  import_custom_routes = false
  export_custom_routes = true

  depends_on = [module.vpc, google_compute_global_address.vpc_peering_ip]
}
