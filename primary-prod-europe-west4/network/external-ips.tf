## CloudNat Reserved IPs
resource "google_compute_address" "bodyscratch-prod-eu-cldnat-ip-1" {
  name         = "${var.base_project_id}-cldnat-ip-1"
  description  = "Cloud Nat IP1"
  address_type = "EXTERNAL"
  network_tier = "PREMIUM"
  project      = local.project_id
  region       = var.region
}

resource "google_compute_address" "bodyscratch-prod-eu-cldnat-ip-2" {
  name         = "${var.base_project_id}-cldnat-ip-2"
  description  = "Cloud Nat IP2"
  address_type = "EXTERNAL"
  network_tier = "PREMIUM"
  project      = local.project_id
  region       = var.region
}

resource "google_compute_address" "bodyscratch-prod-eu-cldnat-ip-3" {
  name         = "${var.base_project_id}-cldnat-ip-3"
  description  = "Cloud Nat IP3"
  address_type = "EXTERNAL"
  network_tier = "PREMIUM"
  project      = local.project_id
  region       = var.region
}

# TODO: move this to DevOps Tools project
resource "google_compute_address" "primary-vpn-external-ip" {
  name         = "${var.base_project_id}-${var.region}-vpn-external-ip"
  description  = "Primary VPN External IP"
  address_type = "EXTERNAL"
  network_tier = "PREMIUM"
  project      = local.project_id
  region       = var.region
}

## Label Studio
resource "google_compute_global_address" "label-studio-external-ip" {
  name         = "label-studio-global-external-ip"
  description  = "Label Studio External IP"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV4"
  labels = {
    "app" = "label-studio"
  }
}

## Keycloak
resource "google_compute_global_address" "keycloak-external-ip" {
  name         = "keycloak-global-external-ip"
  description  = "Keycloak External IP"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV4"
  labels = {
    "app" = "keycloak"
  }
}

## Livekit
resource "google_compute_global_address" "livekit-external-ip" {
  name         = "livekit-global-external-ip"
  description  = "Livekit External IP"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV4"
  labels = {
    "app" = "livekit"
  }
}
## Livekit (TURN)
resource "google_compute_address" "livekit-turn-external-ip" {
  name         = "livekit-turn-${var.region}-external-ip"
  description  = "Livekit (TURN) External IP"
  address_type = "EXTERNAL"
  network_tier = "PREMIUM"
  project      = local.project_id
  region       = var.region
}

## api.bodyscratch.academy
resource "google_compute_global_address" "api-external-ip" {
  name         = "api-global-external-ip"
  description  = "api.bodyscratch.academy External IP"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV4"
  labels = {
    "app" = "api"
  }
}

## app.bodyscratch.academy
resource "google_compute_global_address" "app-web-global-external-ip" {
  name         = "app-web-global-external-ip"
  description  = "app.bodyscratch.academy External IP"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV4"
  labels = {
    "app" = "app-web"
  }
}

## play.bodyscratch.academy
resource "google_compute_global_address" "play-web-global-external-ip" {
  name         = "play-web-global-external-ip"
  description  = "play.bodyscratch.academy External IP"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV4"
  labels = {
    "app" = "play-web"
  }
}

## record-app.bodyscratch.academy
resource "google_compute_global_address" "record-app-web-global-external-ip" {
  name         = "record-app-web-global-external-ip"
  description  = "record-app.bodyscratch.academy External IP"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV4"
  labels = {
    "app" = "record-app-web"
  }
}

## record-control-app.bodyscratch.academy
resource "google_compute_global_address" "record-control-app-web-global-external-ip" {
  name         = "record-control-app-web-global-external-ip"
  description  = "record-control-app.bodyscratch.academy External IP"
  address_type = "EXTERNAL"
  project      = local.project_id
  ip_version   = "IPV4"
  labels = {
    "app" = "record-control-app-web"
  }
}

## Nginx ingress
resource "google_compute_address" "ingress-nginx-europe-west4-external-ip" {
  name         = "ingress-nginx-${var.region}-external-ip"
  description  = "ingress-nginx (${var.region}) External IP"
  address_type = "EXTERNAL"
  network_tier = "PREMIUM"
  project      = local.project_id
  region       = var.region
  labels = {
    "app" = "ingress-nginx"
    "region" = "${var.region}"
  }
}