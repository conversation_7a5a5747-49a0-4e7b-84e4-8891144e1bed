module "pg" {
  source = "**************:BodyScratch/gcp-terraform-modules.git//cloud-sql/modules/postgresql?ref=main"
  project_id           = local.project_id
  region               = var.region
  edition              = "ENTERPRISE"
  name                 = "${var.base_project_id}-main-db"
  random_instance_name = true
  database_version     = "POSTGRES_15"

  // Master configurations
  tier                            = "db-custom-2-13312"
  zone                            = null
  availability_type               = "REGIONAL"
  maintenance_window_day          = 7
  maintenance_window_hour         = 12
  maintenance_window_update_track = "stable"
  disk_size                       = 25
  deletion_protection_enabled     = true
  data_cache_enabled              = true
  encryption_key_name             = "projects/${local.project_id}/locations/${var.region}/keyRings/${var.base_project_id}-keys/cryptoKeys/cloud-sql-crypto-key"


  database_flags = [
  ]

  ip_configuration = {
    ipv4_enabled        = false
    require_ssl         = true
    private_network     = local.network_self_link
    allocated_ip_range  = null
  }

  backup_configuration = {
    enabled                        = true
    binary_log_enabled             = false
    location                       = "eu"
    start_time                     = "20:55"
    point_in_time_recovery_enabled = true
    transaction_log_retention_days = 7
    retained_backups               = 10
    retention_unit                 = "COUNT"
  }
}